
"use strict";
// Sweet Alert CDN through JS
let script = document.createElement("script");
script.type = 'text/javascript';
script.src="https://unpkg.com/sweetalert/dist/sweetalert.min.js";
document.body.appendChild(script);

// Header

let header = $(`
<nav class="navbar navbar-expand-lg fixed-top dark-theme" id="navbar">
<a class="navbar-brand" href="index.html">Mihretab Nigatu</a>
<div class="hamburger_wrapper navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">

  <div id="js-hamburger" class="hamburger">
    <span class="first"></span>
    <span class="second"></span>
    <span class="third"></span>
  </div>

</div>

<div class="collapse navbar-collapse " id="navbarSupportedContent">
  <ul class="navbar-nav ml-auto" id = "navbar-content">
   <li class="nav-item nav-item-hover"><a class="nav-link" href="index.html">Home</a></li>
   <li class="nav-item nav-item-hover"><a class="nav-link" href="experience.html">About</a></li>
   <li class="nav-item nav-item-hover"><a class="nav-link" href="projects.html">Projects</a></li>
   <li class="nav-item nav-item-hover"><a class="nav-link" href="education.html">Education</a></li>
   <li class="nav-item">
   <input type="checkbox" id="dark_toggler" class="dark_toggler" aria-label="Toggle Light Mode" onclick="toggle_light_mode()" checked>
   </li>
   <div class="bike">
   <svg xmlns="http://www.w3.org/2000/svg" viewBox="-80 0 650 400" preserveAspectRatio="xMinYMin meet">
     <rect/>
     <g>
       <g id="man-bike">
         <path id="Vector_2" opacity="0.1" d="M201.091 276.806L179.893 255.76L173.269 262.513L194.467 283.559L201.091 276.806Z" fill="black" />
         <path id="Vector_3" opacity="0.05" d="M269.455 177.344C269.455 177.344 326.601 189.531 325.92 197.189C325.238 204.847 268.194 187.328 268.194 187.328C268.194 187.328 264.731 181.519 269.455 177.344Z" fill="black" />
         <path id="Vector_4" opacity="0.1" d="M259.93 224.494C251.988 220.634 232.6 220.22 232.6 220.22L232.392 218.277L258.907 216.225L260.396 221.718C260.396 221.718 260.729 222.792 259.93 224.494Z" fill="black" />
         <path id="Vector_5" opacity="0.1" d="M202.149 253.613C201.27 254.287 200.833 254.677 200.833 254.677C200.833 254.677 184.95 260.17 184.95 245.688C184.95 245.688 185.719 245.278 186.935 244.594C188.164 246.607 189.885 248.269 191.934 249.423C193.983 250.576 196.292 251.181 198.639 251.181H200.322L202.149 253.613Z" fill="black" />
         <path id="Vector_6" d="M184.95 245.688C184.95 245.688 197.254 239.156 202.01 234.951C204.872 232.432 207.95 230.171 211.207 228.195L226.644 218.742L258.907 216.245L260.396 221.738C260.396 221.738 262.878 229.728 238.06 236.719C213.242 243.71 200.833 254.697 200.833 254.697C200.833 254.697 184.95 260.17 184.95 245.688Z" fill="#DB8B8B" />
         <path id="Vector_7" d="M185.198 242.442L172.044 251.181L210.562 283.64C210.562 283.64 216.22 280.145 213.242 276.15C213.242 276.15 203.315 267.66 202.819 255.675L199.841 251.68H198.158C195.313 251.681 192.537 250.792 190.216 249.137C187.894 247.482 186.141 245.143 185.198 242.442V242.442Z" fill="#E5E5E5" />
         <path id="Vector_8" d="M232.6 219.221C232.6 219.221 255.929 219.72 261.886 224.714L258.907 164.29H226.644L232.6 219.221Z" fill="#6A6567" />
         <path id="Vector_9" d="M201.091 276.806L179.893 255.76L173.269 262.513L194.467 283.559L201.091 276.806Z" fill="#1E1F21" />
         <path id="Vector_10" opacity="0.05" d="M232.6 218.971C232.6 218.971 255.929 219.471 261.886 224.464L258.907 164.04H226.644L232.6 218.971Z" fill="#535461" />
         <path id="Vector_11" opacity="0.05" d="M259.93 224.494C258.55 227.436 253.79 232.27 238.06 236.699C217.903 242.377 205.931 250.682 202.149 253.613C201.27 254.287 200.833 254.677 200.833 254.677C200.833 254.677 184.95 260.17 184.95 245.688C184.95 245.688 185.719 245.278 186.935 244.594C190.623 242.562 198.431 238.102 201.995 234.931C204.857 232.411 207.934 230.15 211.192 228.175L226.629 218.722L232.377 218.277L258.892 216.225L260.382 221.718C260.382 221.718 260.729 222.792 259.93 224.494Z" fill="black" />
         <path id="Vector_12" d="M251.343 182.247C251.343 182.247 309.74 180.25 310.931 187.845C312.122 195.441 252.534 192.24 252.534 192.24C252.534 192.24 247.769 187.441 251.343 182.247Z" fill="#1E1F21" />
         <path id="Vector_13" opacity="0.1" d="M204.07 212.619V219.576C200.635 220.025 197.12 220.31 193.74 220.419V212.619H204.07Z" fill="black" />
         <path id="Vector_14" d="M203.295 205.134C201.846 205.021 200.404 204.826 198.977 204.549C193.378 203.471 179.261 201.208 175.464 205.049C170.699 209.843 172.288 218.237 184.999 219.436C197.711 220.634 219.561 217.438 223.934 212.639C223.934 212.619 216.027 206.117 203.295 205.134Z" fill="#1E1F21" />
         <path id="Vector_15" d="M233.469 290.961L311.73 213.423L316.098 219.815L241.411 298.152L233.469 290.961Z" fill="#71575D" />
         <path id="Vector_16" d="M346.689 303.75L341.129 306.946L302.344 197.918L300.607 193.039L309.745 187.84L312.033 195.021L346.689 303.75Z" fill="#71575D" />
         <path id="Vector_17" d="M204.07 212.624H193.74V302.951H204.07V212.624Z" fill="#71575D" />
         <path id="Vector_18" d="M201.29 237.403L309.745 203.431L313.318 211.825L202.084 250.192L201.29 237.403Z" fill="#1E1F21" />
         <path id="Vector_19" opacity="0.1" d="M178.363 270.347L167.761 280.11C165.883 277.685 163.761 275.462 161.427 273.478L171.325 263.076C173.873 265.293 176.228 267.726 178.363 270.347V270.347Z" fill="black" />
         <path id="Vector_20" d="M252.931 203.431L307.357 184.644C307.357 184.644 316.892 185.843 313.313 193.838L258.495 210.222C258.495 210.222 250.157 211.021 252.931 203.431Z" fill="#1E1F21" />
         <path id="Vector_21" opacity="0.1" d="M231.479 313.743C238.499 313.743 244.19 308.017 244.19 300.954C244.19 293.891 238.499 288.165 231.479 288.165C224.458 288.165 218.767 293.891 218.767 300.954C218.767 308.017 224.458 313.743 231.479 313.743Z" fill="black" />
         <path id="Vector_22" opacity="0.1" d="M333.411 264.594C330.931 265.239 328.513 266.103 326.184 267.176L321.439 253.838C323.929 252.746 326.494 251.833 329.113 251.106L333.411 264.594Z" fill="black" />
         <path id="Vector_23" opacity="0.1" d="M191.556 307.546C191.556 310.609 191.32 313.667 190.851 316.694C186.086 316.35 181.162 315.85 176.338 315.351C177.41 309.413 177.276 303.318 175.946 297.433C180.646 296.434 185.446 295.481 190.132 294.617C191.081 298.86 191.559 303.196 191.556 307.546V307.546Z" fill="black" />
         <path id="Vector_24" d="M228.884 299.993L228.88 299.996C227.387 301.119 227.082 303.247 228.198 304.749L238.287 318.322C239.403 319.824 241.519 320.131 243.011 319.008L243.015 319.005C244.508 317.882 244.813 315.754 243.697 314.252L233.608 300.679C232.492 299.177 230.376 298.87 228.884 299.993Z" fill="#7F879C" />
         <path id="Vector_25" d="M289.682 123.341L288.5 138.377C288.364 140.282 288.389 142.194 288.575 144.095C288.729 145.778 288.575 148.724 286.937 153.218C284.834 158.96 283.747 165.027 283.725 171.146V188.26L272.309 186.262C272.309 186.262 272.805 162.292 270.82 158.297C268.835 154.302 272.309 118.846 272.309 118.846L289.682 123.341Z" fill="#DB8B8B" />
         <path id="Vector_26" d="M233.107 238.197L228.813 262.767C228.272 266.047 227.441 269.272 226.331 272.404C223.815 279.326 225.423 289.029 226.644 294.322C227.185 296.659 227.637 298.137 227.637 298.137L214.731 305.129L210.76 302.132V300.135L210.264 255.69C207.286 248.699 208.279 238.212 210.264 234.217C210.829 232.905 211.226 231.525 211.445 230.112C212.438 224.759 212.746 217.238 212.746 217.238C212.746 217.238 220.688 203.755 230.118 212.744C233.593 216.055 234.913 220.384 235.191 224.529C235.678 231.625 233.107 238.197 233.107 238.197Z" fill="#DB8B8B" />
         <path id="Vector_27" d="M134.549 302.951L196.917 237.403L198.907 252.589L137.726 308.944L134.549 302.951Z" fill="#71575D" />
         <path id="Vector_28" opacity="0.1" d="M235.191 224.514C228.307 228.629 216.33 229.793 211.445 230.097C212.438 224.744 212.746 217.223 212.746 217.223C212.746 217.223 220.688 203.74 230.118 212.729C233.593 216.04 234.913 220.37 235.191 224.514Z" fill="black" />
         <path id="Vector_29" opacity="0.05" d="M289.096 130.792L288.5 138.377C288.366 140.28 288.393 142.191 288.58 144.09C288.734 145.773 288.58 148.719 286.942 153.218C284.839 158.96 283.752 165.027 283.73 171.146V188.26L272.314 186.262C272.314 186.262 272.81 162.292 270.825 158.297C269.42 155.466 270.75 136.854 271.649 126.232C272.021 121.863 272.314 118.846 272.314 118.846L289.687 123.341L289.096 130.792Z" fill="black" />
         <path id="Vector_30" opacity="0.1" d="M254.177 156.499C253.944 158.831 253.944 160.295 253.944 160.295C253.944 160.295 242.527 169.783 223.169 155.301C203.811 140.819 186.439 144.814 186.439 144.814C186.439 144.814 186.642 143.655 187.054 141.818C188.424 138.996 189.913 137.823 189.913 137.823L251.958 149.808L254.177 156.499Z" fill="black" />
         <path id="Vector_31" opacity="0.05" d="M256.058 145.029C254.779 149.51 254.069 154.136 253.944 158.796C253.944 158.796 250.926 161.293 245.367 161.793C245.21 161.475 245.09 161.14 245.009 160.794C244.017 156.3 236.571 148.31 236.571 148.31C231.608 143.815 230.615 135.825 230.615 135.825L222.425 111.605L219.695 109.358C219.695 109.358 231.111 100.37 241.038 107.86C250.966 115.351 247.987 131.331 247.987 131.331L249.973 136.824C252.033 138.572 254.127 141.653 256.058 145.029Z" fill="black" />
         <path id="Vector_32" opacity="0.1" d="M312.033 195.021L302.344 197.918L300.607 193.039L309.745 187.84L312.033 195.021Z" fill="black" />
         <path id="Vector_33" d="M296.373 103.84C295.077 114.003 294.645 126.811 294.645 126.811C283.229 135.8 274.295 124.315 274.295 124.315C263.514 127.226 258.446 136.719 256.058 145.004C254.779 149.485 254.069 154.111 253.944 158.772C253.944 158.772 250.926 161.268 245.367 161.768C240.11 162.212 232.575 160.834 223.169 153.778C203.811 139.296 186.439 143.291 186.439 143.291C186.439 143.291 189.913 123.316 197.855 117.323C205.797 111.331 211.753 103.84 221.68 80.8689C231.608 57.8976 251.958 63.3908 251.958 63.3908C251.958 63.3908 252.951 58.8964 271.316 63.3908C275.79 64.5703 279.93 66.777 283.413 69.8377C293.236 78.1473 298.001 91.0362 296.373 103.84Z" fill="black" />
         <path id="Vector_34" d="M251.958 212.23C246.68 215.058 241.692 218.403 237.068 222.217C229.126 228.709 209.271 229.208 209.271 229.208V221.718C209.768 212.729 201.33 202.742 189.913 174.777C182.622 156.919 184.642 146.811 187.054 141.818C188.424 138.996 189.913 137.823 189.913 137.823L251.958 149.808L254.177 156.499L258.907 170.782C258.907 170.782 241.535 171.78 238.557 173.278C235.578 174.777 237.564 188.26 237.564 188.26C234.586 202.242 251.958 212.23 251.958 212.23Z" fill="#6A6567" />
         <path id="Vector_35" d="M232.278 313.743C239.298 313.743 244.989 308.017 244.989 300.954C244.989 293.891 239.298 288.165 232.278 288.165C225.257 288.165 219.566 293.891 219.566 300.954C219.566 308.017 225.257 313.743 232.278 313.743Z" fill="#1E1F21" />
         <path id="Vector_36" d="M277.953 74.6517C277.928 75.3159 277.873 76.0549 277.784 76.8789C276.791 85.8677 275.798 84.8689 264.879 86.8664C258.088 88.1099 254.559 82.402 252.862 77.9076C252.247 76.2795 251.784 74.5978 251.477 72.8839L275.302 68.8889C275.302 68.8889 278.156 68.0849 277.953 74.6517Z" fill="#DB8B8B" />
         <path id="Vector_37" d="M281.244 63.4157C281.244 68.0512 279.413 72.4969 276.155 75.7747C272.897 79.0524 268.478 80.8939 263.871 80.8939C259.263 80.8939 254.845 79.0524 251.587 75.7747C248.329 72.4969 246.498 68.0512 246.498 63.4157C246.498 63.1261 246.498 62.8365 246.498 62.5468C246.613 57.9113 248.553 53.5115 251.892 50.3152C255.231 47.1188 259.695 45.3879 264.303 45.5031C268.91 45.6184 273.284 47.5703 276.461 50.9296C279.638 54.2888 281.358 58.7802 281.244 63.4157V63.4157Z" fill="#DB8B8B" />
         <path id="Vector_38" opacity="0.1" d="M289.682 123.341L289.096 130.792C280.459 133.288 274.295 125.338 274.295 125.338C273.394 125.581 272.508 125.88 271.644 126.232C272.016 121.863 272.309 118.846 272.309 118.846L289.682 123.341Z" fill="black" />
         <path id="Vector_39" opacity="0.1" d="M242.527 87.8851L249.477 115.351C249.477 115.351 228.133 118.846 223.666 115.351C219.199 111.855 210.76 110.856 210.76 110.856C210.76 110.856 219.695 84.8889 224.659 81.8926C229.622 78.8964 242.527 87.8851 242.527 87.8851Z" fill="black" />
         <path id="Vector_40" opacity="0.1" d="M227.637 298.122L214.731 305.114L210.76 302.117V300.12C215.22 299.801 219.552 298.486 223.442 296.27C224.634 295.59 225.755 294.921 226.654 294.332C227.175 296.644 227.637 298.122 227.637 298.122Z" fill="black" />
         <path id="Vector_41" opacity="0.05" d="M200.089 117.099C200.089 117.099 212.994 120.594 216.965 129.583C220.936 138.572 231.677 140.215 231.677 140.215" fill="black" />
         <path id="Vector_42" d="M221.184 108.859L232.104 135.326C232.104 135.326 233.097 143.316 238.06 147.81C238.06 147.81 245.506 155.8 246.498 160.295C247.491 164.789 261.389 176.774 261.389 176.774C261.389 176.774 274.295 188.759 275.287 193.253C275.287 193.253 287.2 196.749 285.711 203.74C285.711 203.74 294.645 194.752 285.711 186.262C276.776 177.773 264.864 160.295 264.864 160.295C264.864 160.295 257.915 141.818 251.462 136.325L249.477 130.831C249.477 130.831 252.455 114.851 242.527 107.361C232.6 99.8702 221.184 108.859 221.184 108.859Z" fill="#DB8B8B" />
         <path id="Vector_43" d="M248.732 314.851H218.95V324.34H248.732V314.851Z" fill="#1E1F21" />
         <path id="Vector_44" d="M242.527 86.8864L249.477 114.352C249.477 114.352 228.133 117.848 223.666 114.352C219.199 110.856 210.76 109.858 210.76 109.858C210.76 109.858 219.695 83.8901 224.659 80.8939C229.622 77.8976 242.527 86.8864 242.527 86.8864Z" fill="black" />
         <path id="Vector_45" d="M223.423 297.243C226.088 295.745 228.426 294.247 228.629 293.628C228.629 293.628 235.355 300.769 241.346 302.986C243.361 303.735 245.289 304.699 247.099 305.863C249.472 307.361 252.857 309.124 255.929 309.124C255.929 309.124 262.878 315.116 249.477 316.115C236.075 317.114 204.804 313.618 204.804 313.618L206.79 301.134C212.591 301.476 218.366 300.125 223.423 297.243Z" fill="#E5E5E5" />
         <path id="Vector_46" d="M137.329 307.361C137.329 307.361 220.757 285.388 227.508 293.773C234.258 302.157 241.411 313.353 220.375 317.348C199.339 321.343 135.755 310.552 135.755 310.552L137.329 307.361Z" fill="#71575D" />
         <path id="Vector_47" opacity="0.1" d="M248.732 314.851H218.95V324.34H248.732V314.851Z" fill="#1E1F21" />
       </g>
       <g id="left-wheel">
         <path id="Vector_48" d="M135.686 358.047L132.574 309.139L132.509 309.034L129.372 310.132L114.938 352.504L91.9815 336.13L128.548 309.458L128.583 309.353L128.792 309.278L131.149 307.556L92.8104 285.928L83.4193 296.315L84.9084 306.052L128.995 306.861V307.86L84.0844 307.051L82.3868 295.995L95.2426 281.783L114.477 259.211L114.819 260.145L118.889 255.65L134.008 258.377V257.773L135.001 257.803V258.557L157.516 262.612L136.525 304.864L133.462 307.096V307.346V307.625L182.006 290.607L176.244 336.155L135.046 309.753L133.596 308.934L157.888 348.325L135.686 358.047ZM133.701 310.936L136.604 356.569L156.483 347.89L133.701 310.936ZM93.7039 336.12L114.447 350.916L128.017 311.096L93.7039 336.12ZM134.564 308.325L135.602 308.909L175.484 334.477L180.845 292.095L134.564 308.325ZM131.04 304.729L132.286 306.752L132.504 306.592L133.993 259.386L119.232 256.729L115.181 261.223L131.04 304.729ZM93.6046 285.223L130.752 306.197L130.107 305.134L114.432 262.052L95.9722 282.462L93.6046 285.223ZM135.001 259.561L133.512 305.863L135.755 304.225L156.062 263.366L135.001 259.561Z" fill="#535461" />
         <path id="Vector_49" d="M132.956 313.743C136.468 313.743 139.314 310.879 139.314 307.346C139.314 303.813 136.468 300.949 132.956 300.949C129.444 300.949 126.598 303.813 126.598 307.346C126.598 310.879 129.444 313.743 132.956 313.743Z" fill="#1E1F21" />
         <path id="Vector_50" d="M190.132 294.607C188.141 285.708 184.111 277.401 178.363 270.347C176.229 267.726 173.876 265.293 171.33 263.076C161.538 254.571 149.217 249.578 136.3 248.88C123.383 248.181 110.601 251.818 99.9599 259.218C89.3191 266.619 81.4219 277.365 77.5077 289.769C73.5935 302.173 73.8838 315.534 78.3331 327.754C82.7824 339.975 91.1389 350.363 102.091 357.288C113.043 364.214 125.971 367.285 138.845 366.02C151.72 364.754 163.812 359.224 173.225 350.296C182.637 341.369 188.837 329.55 190.851 316.694C191.32 313.667 191.556 310.609 191.556 307.546C191.56 303.193 191.082 298.853 190.132 294.607ZM132.956 351.71C123.906 351.701 115.079 348.887 107.675 343.653C100.27 338.418 94.648 331.016 91.5724 322.453C88.4968 313.89 88.1171 304.582 90.4849 295.795C92.8527 287.007 97.8531 279.167 104.806 273.339C111.759 267.512 120.328 263.98 129.346 263.224C138.365 262.468 147.396 264.526 155.211 269.116C163.027 273.706 169.247 280.606 173.027 288.879C176.807 297.151 177.963 306.394 176.338 315.351C174.476 325.56 169.116 334.789 161.19 341.432C153.263 348.075 143.273 351.713 132.956 351.71Z" fill="#1E1F21" />
       </g>
       <g id="right-wheel">
         <path id="Vector_51" d="M347.23 358.247L344.122 309.358L344.058 309.253L340.921 310.352L326.482 352.704L303.525 336.325L340.102 309.653L340.137 309.548L340.345 309.473L342.703 307.75L304.364 286.122L294.968 296.509L296.457 306.247L340.534 307.036V308.035L295.623 307.226L293.935 296.185L306.786 281.978L326.005 259.421L326.348 260.355L330.418 255.86L345.577 258.592V257.988L346.569 258.017V258.772L369.084 262.826L348.073 305.099L345.011 307.331V307.581V307.86L393.56 290.841L387.797 336.389L346.599 309.987L345.15 309.169L369.442 348.564L347.23 358.247ZM345.244 311.136L348.148 356.769L368.027 348.09L345.244 311.136ZM305.252 336.325L325.995 351.121L339.566 311.301L305.252 336.325ZM346.113 308.529L347.15 309.114L387.033 334.682L392.393 292.3L346.113 308.529ZM342.589 304.934L343.835 306.956L344.053 306.796L345.542 259.591L330.78 256.924L326.73 261.418L342.589 304.934ZM305.168 285.433L342.316 306.407L341.67 305.343L325.995 262.262L307.531 282.677L305.168 285.433ZM346.55 259.77L345.061 306.057L347.304 304.419L367.61 263.561L346.55 259.77Z" fill="#535461" />
         <path id="Vector_52" d="M344.505 313.943C348.016 313.943 350.863 311.079 350.863 307.546C350.863 304.013 348.016 301.149 344.505 301.149C340.993 301.149 338.146 304.013 338.146 307.546C338.146 311.079 340.993 313.943 344.505 313.943Z" fill="#1E1F21" />
         <path id="Vector_53" d="M344.703 248.994C339.435 248.988 334.191 249.7 329.113 251.111C326.494 251.838 323.929 252.751 321.439 253.843C308.171 259.629 297.508 270.161 291.512 283.4C285.516 296.639 284.614 311.645 288.979 325.515C293.345 339.385 302.668 351.133 315.146 358.486C327.624 365.84 342.369 368.276 356.529 365.324C370.689 362.372 383.257 354.242 391.803 342.505C400.349 330.769 404.265 316.261 402.793 301.788C401.321 287.315 394.566 273.905 383.835 264.153C373.104 254.401 359.159 249 344.698 248.994H344.703ZM344.505 351.91C334.438 351.906 324.676 348.438 316.839 342.081C309.002 335.724 303.562 326.862 301.423 316.965C299.284 307.069 300.574 296.734 305.079 287.677C309.584 278.62 317.033 271.386 326.189 267.176C328.518 266.103 330.936 265.24 333.416 264.594C339.478 263.011 345.805 262.745 351.977 263.814C358.148 264.883 364.023 267.262 369.21 270.792C374.397 274.323 378.777 278.925 382.058 284.291C385.34 289.657 387.448 295.665 388.242 301.915C389.036 308.165 388.498 314.513 386.664 320.537C384.829 326.561 381.741 332.123 377.603 336.853C373.466 341.583 368.375 345.372 362.669 347.968C356.962 350.564 350.772 351.908 344.51 351.91H344.505Z" fill="#1E1F21" />
       </g>
       <path id="hat" d="M233.931 52.6043C234.988 55.9401 240.066 56.045 242.671 58.5019C244.026 59.7803 244.657 61.6679 246.146 62.8065C248.216 64.3945 251.204 64.045 253.839 63.6654C259.146 62.9064 264.844 62.4519 269.629 64.7541C271.326 65.568 272.85 66.7066 274.622 67.3708C276.394 68.035 278.593 68.1149 280.032 66.9363C281.184 65.9925 281.606 64.4395 282.648 63.4008C283.457 62.5818 284.584 62.1224 285.507 61.4033C287.532 59.8652 288.406 57.3583 288.982 54.9613C290.173 50.0275 290.516 44.7091 288.485 40.03C286.455 35.3508 281.571 31.5406 276.24 31.7853C274.607 31.8602 273.014 32.2846 271.386 32.4644C267.142 32.8989 262.948 31.4657 258.689 31.221C257.454 31.0697 256.2 31.2417 255.051 31.7204C254.184 32.2052 253.436 32.8792 252.862 33.6929C250.912 36.2624 249.577 39.2494 248.96 42.422C248.464 44.814 248.439 46.9813 245.525 47.3508C242.215 47.7903 231.692 45.5481 233.931 52.6043Z" fill="#472727" />
     </g>
     <defs>
       <linearGradient id="paint0_linear" x1="432.641" y1="56.6686" x2="44.1018" y2="338.225" gradientUnits="userSpaceOnUse">
         <stop offset="0.0001" stop-color="#490DF3" />
         <stop offset="1" stop-color="#3B49C6" stop-opacity="0.05" />
       </linearGradient>
     </defs>
   </svg>
 </div>
  </ul>
</div>
</nav>`);

// Footer

let footer = $(`
<footer class="footer sticky-bottom"  style="background-color:#2b2a2a;">
  <div class="p-4">
    <div class="container-fluid quote-container">
      <div class="quotes"    style="text-align:center;">
      <!--SVG code for quote-left icon-->
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" focusable="false" width="1.5em" height="1.5em" style="-ms-transform: rotate(360deg); -webkit-transform: rotate(360deg); transform: rotate(360deg);" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24"><path d="M7 21a4 4 0 0 1-4-4c0-1.473 1.333-6.14 4-14h2L7 13a4 4 0 1 1 0 8zm10 0a4 4 0 0 1-4-4c0-1.473 1.333-6.14 4-14h2l-2 10a4 4 0 1 1 0 8z" fill="#fff" fill-rule="evenodd"/></svg>
      </div>
      <p class="tag" >Develop a passion for learning. If you do, you will never cease to grow.</p>
    </div>

    
    <div class="container">
      <div class="row">
        <div class="col-lg-6 col-md-12 mb-4 mb-md-0 justify-content-center">
          <style>svg#freepik_stories-smiley-face:not(.animated) .animable {opacity: 0;}svg#freepik_stories-smiley-face.animated #freepik--background-complete--inject-31 {animation: 1s 1 forwards cubic-bezier(.36,-0.01,.5,1.38) fadeIn;animation-delay: 0.2s;opacity: 0}svg#freepik_stories-smiley-face.animated #freepik--Shadow--inject-31 {animation: 1s 1 forwards cubic-bezier(.36,-0.01,.5,1.38) fadeIn;animation-delay: 0.2s;opacity: 0}svg#freepik_stories-smiley-face.animated #freepik--Character--inject-31 {animation: 1s 1 forwards cubic-bezier(.36,-0.01,.5,1.38) slideDown,1.5s Infinite  linear floating;animation-delay: 0.2s,1.2s;opacity: 0}            @keyframes fadeIn {                0% {                    opacity: 0;                }                100% {                    opacity: 1;                }            }                    @keyframes slideDown {                0% {                    opacity: 0;                    transform: translateY(-30px);                }                100% {                    opacity: 1;                    transform: translateY(0);                }            }                    @keyframes floating {                0% {                    opacity: 1;                    transform: translateY(0px);                }                50% {                    transform: translateY(-10px);                }                100% {                    opacity: 1;                    transform: translateY(0px);                }            }        </style>
              <svg class="animated" id="freepik_stories-smiley-face" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 500" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs"><g id="freepik--background-complete--inject-31" class="animable" style="transform-origin: 250px 207.723px;"><rect y="382.4" width="500" height="0.25" style="fill: rgb(235, 235, 235); transform-origin: 250px 382.525px;" id="elz12x8mjcuy" class="animable"></rect><rect x="359" y="391.92" width="90.9" height="0.25" style="fill: rgb(235, 235, 235); transform-origin: 404.45px 392.045px;" id="ele8kmyfspz6b" class="animable"></rect><rect x="306.33" y="389.21" width="24.89" height="0.25" style="fill: rgb(235, 235, 235); transform-origin: 318.775px 389.335px;" id="eledad4o6o5d" class="animable"></rect><rect x="349.52" y="401.21" width="38.59" height="0.25" style="fill: rgb(235, 235, 235); transform-origin: 368.815px 401.335px;" id="el2n7z79wktuo" class="animable"></rect><rect x="52.46" y="399.53" width="22.64" height="0.25" style="fill: rgb(235, 235, 235); transform-origin: 63.78px 399.655px;" id="elvxkkohbhat" class="animable"></rect><rect x="84" y="399.53" width="26.89" height="0.25" style="fill: rgb(235, 235, 235); transform-origin: 97.445px 399.655px;" id="el1hzhibkohdp" class="animable"></rect><rect x="178.31" y="395.31" width="46.84" height="0.25" style="fill: rgb(235, 235, 235); transform-origin: 201.73px 395.435px;" id="elhzpvwn605h7" class="animable"></rect><path d="M237,337.8H43.91a5.71,5.71,0,0,1-5.7-5.71V60.66A5.71,5.71,0,0,1,43.91,55H237a5.71,5.71,0,0,1,5.71,5.71V332.09A5.71,5.71,0,0,1,237,337.8ZM43.91,55.2a5.46,5.46,0,0,0-5.45,5.46V332.09a5.46,5.46,0,0,0,5.45,5.46H237a5.47,5.47,0,0,0,5.46-5.46V60.66A5.47,5.47,0,0,0,237,55.2Z" style="fill: rgb(235, 235, 235); transform-origin: 140.46px 196.4px;" id="el92bi8cxpvav" class="animable"></path><path d="M453.31,337.8H260.21a5.72,5.72,0,0,1-5.71-5.71V60.66A5.72,5.72,0,0,1,260.21,55h193.1A5.71,5.71,0,0,1,459,60.66V332.09A5.71,5.71,0,0,1,453.31,337.8ZM260.21,55.2a5.47,5.47,0,0,0-5.46,5.46V332.09a5.47,5.47,0,0,0,5.46,5.46h193.1a5.47,5.47,0,0,0,5.46-5.46V60.66a5.47,5.47,0,0,0-5.46-5.46Z" style="fill: rgb(235, 235, 235); transform-origin: 356.75px 196.4px;" id="elpbqyu6v2yks" class="animable"></path><g id="ellpbg5sgx4tl"><circle cx="122.74" cy="109.59" r="70.27" style="fill: rgb(235, 235, 235); transform-origin: 122.74px 109.59px; transform: rotate(-45deg);" class="animable" id="elqv1umdpmofn"></circle></g><path d="M69.64,99c.76,1.37,2.66,1.61,4.24.54s2.24-3,1.48-4.42-2.66-1.61-4.24-.54S68.88,97.61,69.64,99Z" style="fill: rgb(255, 255, 255); transform-origin: 72.5px 97.0522px;" id="elwyii447cnes" class="animable"></path><path d="M101.26,83.17a3.46,3.46,0,1,0,2.39-3.76A3.19,3.19,0,0,0,101.26,83.17Z" style="fill: rgb(255, 255, 255); transform-origin: 104.671px 82.7102px;" id="ellxn1f8vo9z" class="animable"></path><path d="M107.44,93.87c.85,1.71,4.56,1.29,8.28-.92s6-5.38,5.19-7.08-4.56-1.29-8.28.92S106.59,92.17,107.44,93.87Z" style="fill: rgb(240, 240, 240); transform-origin: 114.169px 89.8721px;" id="eld2400dcgy64" class="animable"></path><path d="M63.38,111.81c.61,1.74,4.25,2,8.13.58s6.54-4,5.93-5.73-4.24-2-8.12-.58S62.78,110.07,63.38,111.81Z" style="fill: rgb(240, 240, 240); transform-origin: 70.4113px 109.237px;" id="elaqe9wyt3w7f" class="animable"></path><path d="M113.55,99.45c-6.45,1.14-22.88,5-37.72,16.07a2.53,2.53,0,0,0-.32,3.34C85.68,134.62,112.74,123,116,102.4,116.3,100.77,115,99.2,113.55,99.45Z" style="fill: rgb(224, 224, 224); transform-origin: 95.5343px 112.803px;" id="ela0xzo5jc17k" class="animable"></path><path d="M90.41,116a12.06,12.06,0,0,1,3.27-.21,9.92,9.92,0,0,1,6.65-4.42,11.72,11.72,0,0,1,10.58,3,30.11,30.11,0,0,1-8.06,7.52,27.74,27.74,0,0,1-7.53,3.41A19.37,19.37,0,0,1,82.73,125C82.27,120.87,85.44,117,90.41,116Z" style="fill: rgb(255, 255, 255); transform-origin: 96.7979px 118.657px;" id="elnfdxc6486h" class="animable"></path><path d="M396,72.5a20.49,20.49,0,0,0,2.94,5,1.36,1.36,0,0,0,2.39-.81c.16-2.36,1.08-4.09,2.68-4.59a3.44,3.44,0,0,1,1.78-.06,1.37,1.37,0,0,0,1.65-1.73c-.15-.42-.3-.84-.48-1.26-2.33-5.67-6.67-9.51-9.7-8.56S393.68,66.82,396,72.5Z" style="fill: rgb(235, 235, 235); transform-origin: 401.034px 69.16px;" id="el6nwl8k97tju" class="animable"></path><g id="elf9faotbb9hs"><circle cx="358.27" cy="103.18" r="57.3" style="fill: rgb(245, 245, 245); transform-origin: 358.27px 103.18px; transform: rotate(-45deg);" class="animable" id="elqi86kkazii9"></circle></g><g id="eltc10qpfw589"><circle cx="110.29" cy="305.94" r="48.64" style="fill: rgb(250, 250, 250); transform-origin: 110.29px 305.94px; transform: rotate(-45deg);" class="animable" id="elbkp2k6by87d"></circle></g><path d="M85.38,339.06a19.78,19.78,0,0,1,4.46,2.57,1.07,1.07,0,0,1-.74,1.91c-2.1,0-3.66.69-4.11,2a2.47,2.47,0,0,0-.06,1.43,1.13,1.13,0,0,1-1.55,1.26c-.38-.13-.75-.27-1.13-.43-5-2.12-8.44-5.78-7.57-8.19S80.33,337,85.38,339.06Z" style="fill: rgb(235, 235, 235); transform-origin: 82.3982px 342.983px;" id="elpaf77a8n7sa" class="animable"></path><path d="M102,306.39a9.92,9.92,0,0,1,4,2.75c.76.86.37,3.4-.6,4a7.59,7.59,0,0,0-3.58,5.7,10.13,10.13,0,0,0,0,2.75c.19,1.54-.53,3.31-1.34,3.13a10,10,0,0,1-1-.28c-4.51-1.6-7.6-6.92-6.9-11.89S97.49,304.8,102,306.39Z" style="fill: rgb(235, 235, 235); transform-origin: 99.4407px 315.337px;" id="el2ptuoqt91zm" class="animable"></path><g id="els31dqseik8"><circle cx="355.9" cy="275.72" r="91.58" style="fill: rgb(240, 240, 240); transform-origin: 355.9px 275.72px; transform: rotate(-45deg);" class="animable" id="el7qamk7i2df9"></circle></g><path d="M338.37,309.46c-2.78,2.94-.1,7.21,6,9.53s13.29,1.8,16.07-1.15.09-7.21-6-9.52S341.15,306.51,338.37,309.46Z" style="fill: rgb(255, 255, 255); transform-origin: 349.404px 313.655px;" id="el8o56g9k4os5" class="animable"></path><path d="M404.76,316.39c.28,3.17,5.35,4.52,11.32,3s10.58-5.31,10.3-8.48-5.35-4.53-11.31-3S404.48,313.21,404.76,316.39Z" style="fill: rgb(255, 255, 255); transform-origin: 415.57px 313.648px;" id="elv9mhsatd7dl" class="animable"></path><path d="M344,338.53c10.72,1.76,32.24,4.77,50.43,4.06a2.29,2.29,0,0,1,1.46,4.13c-11.5,8.63-34.53,13.76-53.83-4.27A2.29,2.29,0,0,1,344,338.53Z" style="fill: rgb(224, 224, 224); transform-origin: 369.084px 346.543px;" id="elb8wu07kh5ik" class="animable"></path></g><g id="freepik--Shadow--inject-31" class="animable" style="transform-origin: 250px 416.24px;"><ellipse id="freepik--path--inject-31" cx="250" cy="416.24" rx="193.89" ry="11.32" style="fill: rgb(245, 245, 245); transform-origin: 250px 416.24px;" class="animable"></ellipse></g><g id="freepik--Character--inject-31" class="animable" style="transform-origin: 200.694px 236.008px;"><polygon points="271.81 407.29 264.32 404.2 270.35 386.07 277.84 389.16 271.81 407.29" style="fill: rgb(255, 139, 123); transform-origin: 271.08px 396.68px;" id="elc60u85tlpt4" class="animable"></polygon><polygon points="344.44 407.9 336.27 407.9 335.55 388.99 343.72 388.99 344.44 407.9" style="fill: rgb(255, 139, 123); transform-origin: 339.995px 398.445px;" id="elokf2bmtts4o" class="animable"></polygon><path d="M335.23,407h10a.69.69,0,0,1,.67.56l1.27,7.26a1.26,1.26,0,0,1-1.26,1.45c-3.2-.05-5.53-.24-9.56-.24-2.49,0-7.61.26-11,.26s-3.72-3.39-2.3-3.7c6.35-1.38,8.81-3.27,10.91-5.08A2.06,2.06,0,0,1,335.23,407Z" style="fill: rgb(38, 50, 56); transform-origin: 334.721px 411.645px;" id="elw965xu7ard" class="animable"></path><path d="M263.86,402.65l8.25,3.39a.77.77,0,0,1,.46.8l-.88,7.49a1.11,1.11,0,0,1-1.56.92c-2.95-1.27-7.19-3.22-10.93-4.76-4.38-1.79-6.27-2.29-11.39-4.4-3.1-1.28-2.86-4.84-1.46-4.6,6.37,1.11,9.27,2.14,15.49,1A3.66,3.66,0,0,1,263.86,402.65Z" style="fill: rgb(38, 50, 56); transform-origin: 258.968px 408.413px;" id="elgiznyosr41v" class="animable"></path><path d="M314.41,167c-1.06,1-2,1.74-3,2.54s-2,1.57-3,2.34c-2,1.54-4.13,2.95-6.28,4.33s-4.39,2.64-6.66,3.85a76.09,76.09,0,0,1-7.16,3.23c-1.23.49-2.52.89-3.8,1.3-.59.21-1.43.4-2.17.58a18.9,18.9,0,0,1-2.41.36,16.53,16.53,0,0,1-10-2.09,16.07,16.07,0,0,1-3.84-3.13,19.1,19.1,0,0,1-2.64-3.73,22.83,22.83,0,0,1-1.69-3.93c-.22-.67-.39-1.34-.55-2s-.26-1.3-.39-2.15L266.8,167l.74,1.28c.27.45.56.9.85,1.33a19.36,19.36,0,0,0,1.86,2.36,13.23,13.23,0,0,0,2,1.79,8.21,8.21,0,0,0,2.05,1,7.22,7.22,0,0,0,4.1,0c.37-.08.76-.23,1.15-.34s.74-.27,1.28-.5c1-.42,2-.81,2.95-1.31a84.07,84.07,0,0,0,11.54-6.79c1.89-1.27,3.73-2.62,5.57-4,.92-.68,1.83-1.38,2.72-2.09l2.57-2.07Z" style="fill: rgb(255, 139, 123); transform-origin: 287.615px 171.643px;" id="elo2wt8hp4pjd" class="animable"></path><path d="M319.22,164.71c-3.49,7.16-16.75,13.06-16.75,13.06l-5-16s2.14-2.9,7.58-5.5C317.89,150.15,323.92,155.06,319.22,164.71Z" style="fill: rgb(38, 50, 56); transform-origin: 309.139px 165.67px;" id="elvlqn45kl9b" class="animable"></path><g id="eleh712xjy8ad"><path d="M310.86,164.41a34.06,34.06,0,0,0-6.93,10.68,23.05,23.05,0,0,0-.81,2.38c2-1,8.41-4.2,12.74-8.37l.33-1.93A3.16,3.16,0,0,0,310.86,164.41Z" style="opacity: 0.2; transform-origin: 309.675px 170.489px;" class="animable" id="elhi72sn5dwrg"></path></g><path d="M268.71,170.68l-4.46-9.58-5.47,9.07s2.67,5.49,8.16,3.57Z" style="fill: rgb(255, 139, 123); transform-origin: 263.745px 167.624px;" id="elcvugl93uee" class="animable"></path><polygon points="257.51 156.82 253.8 163.66 258.78 170.17 264.25 161.1 257.51 156.82" style="fill: rgb(255, 139, 123); transform-origin: 259.025px 163.495px;" id="eloyef4kodfkg" class="animable"></polygon><g id="elqphaagxj41c"><polygon points="270.35 386.08 267.24 395.42 274.74 398.51 277.85 389.17 270.35 386.08" style="opacity: 0.2; transform-origin: 272.545px 392.295px;" class="animable" id="el0n2drzlzex8q"></polygon></g><g id="eldr68w7fq6fh"><polygon points="343.72 388.99 335.55 388.99 335.93 398.74 344.1 398.74 343.72 388.99" style="opacity: 0.2; transform-origin: 339.825px 393.865px;" class="animable" id="elsngt2bknpjf"></polygon></g>
                <path d="M308.84,158.77a367,367,0,0,0-4.69,65.62l43.31,5.68c.75-6.36,1.84-35.6,6.93-66.27.42-2.52.55-4.45-1.92-5.16-2.73-.79-8.08-2.27-11.69-2.93a161.51,161.51,0,0,0-18.41-2.41,73.89,73.89,0,0,0-7.59.13A6.45,6.45,0,0,0,308.84,158.77Z" style="fill: rgb(38, 50, 56); transform-origin: 329.358px 191.663px;" id="el946yuot26mr" class="animable"></path><path d="M340.78,155.71a161.09,161.09,0,0,0-18.42-2.41c-1.33-.05-2.64-.05-3.89,0l1.3,1.86a31.74,31.74,0,0,1,3.69,7.7h0a1.27,1.27,0,0,0,1.66.82l1.94-.74c3.2-1.21,8.51-2.63,11.73-3.44a49.17,49.17,0,0,0,5.67-1.81l1.84-.7C344.38,156.47,342.4,156,340.78,155.71Z" style="fill: rgb(255, 255, 255); transform-origin: 332.385px 158.512px;" id="elljcq3lr6ure" class="animable"></path><g id="el0esqeh2teg5c"><path d="M341.75,173a28.6,28.6,0,0,0-1.32,15.67,3.13,3.13,0,0,0,3.58,2.53l3.51-.55a3.15,3.15,0,0,0,2.61-2.59l3.87-23a3.15,3.15,0,0,0-5.32-2.76A33.72,33.72,0,0,0,341.75,173Z" style="opacity: 0.2; transform-origin: 346.967px 176.316px;" class="animable" id="eltzbbonv4ltk"></path></g><path d="M340.47,134.58l-12,8.24c.82,4.79.63,9.75-6.1,10.48a35.77,35.77,0,0,1,3.15,5.51,1.26,1.26,0,0,0,1.58.72c5.26-1.83,13.69-3.82,13.69-3.82C336.92,151.18,338.88,140.4,340.47,134.58Z" style="fill: rgb(255, 139, 123); transform-origin: 331.58px 147.09px;" id="el0uec5nugxxfr" class="animable"></path><g id="el6bt12hi7asb"><path d="M335.57,138l-7.1,4.87a20.67,20.67,0,0,1,.33,3.37c2.55,0,6.22-2.37,6.68-5A9,9,0,0,0,335.57,138Z" style="opacity: 0.2; transform-origin: 332.073px 142.12px;" class="animable" id="el11ytindgg50o"></path></g><path d="M328.8,111.66c-5.7,2.73-8.72,15.69-.6,12.79S335,108.69,328.8,111.66Z" style="fill: rgb(38, 50, 56); transform-origin: 328.552px 118.044px;" id="elpt58zhbkfa" class="animable"></path><path d="M347.21,126.48c-2.49,7.61-4.79,13.3-9.76,16-7.47,4.11-16.15-2.52-16-10.86.14-7.51,5.42-19.78,13.85-20.44A11.75,11.75,0,0,1,347.21,126.48Z" style="fill: rgb(255, 139, 123); transform-origin: 334.607px 127.445px;" id="elv8x2dnqpqjj" class="animable"></path><path d="M341.26,124.72c1.47-1.88,6.72-6.59,1.7-6.1s-14.84-1.23-17.74-7.09,4.23-5.49,5.71-3.23c-.51-3.22,1.61-5.62,4.16-4.45a6.66,6.66,0,0,1,3.35,3.7s.68-5.39,4.4-2.56a15.3,15.3,0,0,1,4.94,6.66s11.67,5,5.59,9.34c5.33,1,4,11.19-9,12.55C337.86,132.4,339.08,127.5,341.26,124.72Z" style="fill: rgb(38, 50, 56); transform-origin: 340.419px 118.544px;" id="el3vrz7e7oaja" class="animable"></path><path d="M354.33,121.33a4,4,0,0,1-3.33-1.39.27.27,0,0,1,.43-.31c.82,1.17,2.72,1.36,4.13,1,.9-.2,1.35-.55,1.39-.79s-.23-.4-.4-.51a.26.26,0,0,1-.08-.36.26.26,0,0,1,.36-.09c.61.39.69.79.64,1-.09.53-.76,1-1.79,1.22A6.5,6.5,0,0,1,354.33,121.33Z" style="fill: rgb(38, 50, 56); transform-origin: 354.229px 120.091px;" id="elgna1iuhbjx5" class="animable"></path><path d="M347.89,134.63a7.86,7.86,0,0,1-5.09,2.68c-2.66.29-3.54-2-2.42-4.17,1-1.93,3.59-4.4,6.07-3.69A3.11,3.11,0,0,1,347.89,134.63Z" style="fill: rgb(255, 139, 123); transform-origin: 344.339px 133.33px;" id="el5xsaybph3i4" class="animable"></path><path d="M304.15,224.39S290.53,282.2,285,306.59C279.16,332,265,392.47,265,392.47l12.8,5.26s14.46-49,22.23-73.26c8.48-26.43,31-75,32.57-96.35Z" style="fill: #bb86fc; transform-origin: 298.8px 311.06px;" id="elu3ra9n0vuvr" class="animable"></path><g id="el4ms1cvt8qk3"><path d="M316.85,279.83c4.64-12,9.11-24,12.11-34.29h0C318.27,240.46,316,260.18,316.85,279.83Z" style="opacity: 0.2; transform-origin: 322.824px 262.28px;" class="animable" id="elomz9f5siwel"></path></g><path d="M322.44,226.79s-1.75,64.26.62,88.4c2.6,26.43,9,81.66,9,81.66h15s-1.94-54-2.21-79.77c-.29-28,1.14-66.56,2.63-87Z" style="fill: #bb86fc; transform-origin: 334.649px 311.82px;" id="eli4p1ct2pom" class="animable"></path><polygon points="331.86 397.18 348.18 397.18 348.18 391.57 330.05 391.57 331.86 397.18" style="fill: #bb86fc; transform-origin: 339.115px 394.375px;" id="elybggkgwy7w" class="animable"></polygon><g id="elzfon7s5yb0a"><polygon points="331.86 397.18 348.18 397.18 348.18 391.57 330.05 391.57 331.86 397.18" style="opacity: 0.4; transform-origin: 339.115px 394.375px;" class="animable" id="elsnse9tg2ep"></polygon></g><polygon points="263.36 392.15 278.74 398.53 281.19 393.71 264.52 386.56 263.36 392.15" style="fill: #bb86fc; transform-origin: 272.275px 392.545px;" id="el7bsk8ohj0wq" class="animable"></polygon><g id="elatnyhjvt69m"><polygon points="263.36 392.15 278.74 398.53 281.19 393.71 264.52 386.56 263.36 392.15" style="opacity: 0.4; transform-origin: 272.275px 392.545px;" class="animable" id="eln0295pc960o"></polygon></g><path d="M333.62,126c-.17.63-.64,1-1.05.9s-.62-.75-.45-1.38.63-1,1-.91S333.78,125.34,333.62,126Z" style="fill: rgb(38, 50, 56); transform-origin: 332.863px 125.756px;" id="elax0lxbklnq6" class="animable"></path><path d="M326.45,123.68c-.16.63-.63,1-1.05.9s-.61-.75-.45-1.38.63-1,1.05-.9S326.62,123.05,326.45,123.68Z" style="fill: rgb(38, 50, 56); transform-origin: 325.702px 123.44px;" id="elewdgpqhr7bg" class="animable"></path><path d="M328.65,124.72a21.55,21.55,0,0,1-4.44,4.44,4,4,0,0,0,2.88,1.53Z" style="fill: rgb(255, 86, 82); transform-origin: 326.43px 127.705px;" id="elk982gv6wy4d" class="animable"></path><path d="M328.49,133a5.26,5.26,0,0,0,5-.72.19.19,0,0,0,0-.27.18.18,0,0,0-.27,0,5,5,0,0,1-4.61.65.17.17,0,0,0-.23.12A.21.21,0,0,0,328.49,133Z" style="fill: rgb(38, 50, 56); transform-origin: 330.962px 132.641px;" id="elbvpzy66yy06" class="animable"></path><path d="M337.17,122.89a.36.36,0,0,0,.25,0,.37.37,0,0,0,.23-.49,4.13,4.13,0,0,0-2.5-2.63.37.37,0,0,0-.47.27.41.41,0,0,0,.29.48h0a3.33,3.33,0,0,1,2,2.11A.4.4,0,0,0,337.17,122.89Z" style="fill: rgb(38, 50, 56); transform-origin: 336.174px 121.333px;" id="elnbodgf37rw" class="animable"></path><path d="M324.68,119.56a.36.36,0,0,0,.39-.1,2.9,2.9,0,0,1,2.53-1,.36.36,0,0,0,.43-.31.42.42,0,0,0-.33-.45,3.59,3.59,0,0,0-3.22,1.23.39.39,0,0,0,0,.55A.29.29,0,0,0,324.68,119.56Z" style="fill: rgb(38, 50, 56); transform-origin: 326.199px 118.624px;" id="el9wx9gvsjtjl" class="animable"></path><path d="M358.63,168.06c-.16,1.82-.36,3.38-.62,5s-.53,3.27-.84,4.9c-.63,3.24-1.4,6.48-2.36,9.7a78,78,0,0,1-3.5,9.59,45.08,45.08,0,0,1-5.42,9.33l-.44.56-.23.28-.39.47a10.55,10.55,0,0,1-1.8,1.58,14.24,14.24,0,0,1-3.35,1.72,27,27,0,0,1-5.74,1.28,60.24,60.24,0,0,1-10.26.23,114.5,114.5,0,0,1-19.34-2.87l.81-6.07c3.06,0,6.21,0,9.29-.05s6.17-.16,9.17-.39a62.4,62.4,0,0,0,8.62-1.14,15.54,15.54,0,0,0,3.41-1.1,2.79,2.79,0,0,0,.82-.52c.05,0,0-.06,0,0s-.07.08,0,0l.13-.2.26-.39a39.26,39.26,0,0,0,3.59-7.29A83.35,83.35,0,0,0,343,184.4c.74-2.87,1.38-5.8,1.94-8.75.28-1.48.54-3,.77-4.45s.46-3,.62-4.35Z" style="fill: rgb(255, 139, 123); transform-origin: 331.485px 189.834px;" id="eli3m5k9dxgk" class="animable"></path><path d="M359.65,164.46c4,9.37-2.49,22.43-2.49,22.43l-15.51-5.45a137.64,137.64,0,0,1,2.08-16.54C345.41,155.59,356.56,157.24,359.65,164.46Z" style="fill: rgb(38, 50, 56); transform-origin: 351.301px 172.681px;" id="elm02m2cvzvi" class="animable"></path><path d="M332.13,408.38a9.87,9.87,0,0,0,2.12-.29.17.17,0,0,0,.13-.16.19.19,0,0,0-.1-.19c-.3-.19-3-1.81-3.91-1.35a.6.6,0,0,0-.3.55,1.22,1.22,0,0,0,.46,1A2.6,2.6,0,0,0,332.13,408.38Zm1.54-.56a4.07,4.07,0,0,1-2.91-.15.87.87,0,0,1-.3-.7.23.23,0,0,1,.11-.23C331,406.5,332.57,407.2,333.67,407.82Z" style="fill: #bb86fc; transform-origin: 332.225px 407.346px;" id="elwlek70eu9ad" class="animable"></path><path d="M334.2,408.09a.2.2,0,0,0,.11,0,.2.2,0,0,0,.07-.18c0-.1-.34-2.45-1.34-3.23a1.12,1.12,0,0,0-.83-.25.6.6,0,0,0-.59.51c-.12.91,1.62,2.7,2.49,3.16Zm-1.84-3.31a.77.77,0,0,1,.46.17,5,5,0,0,1,1.11,2.56c-.88-.64-2-2-1.93-2.53,0-.05,0-.16.28-.19Z" style="fill: #bb86fc; transform-origin: 332.998px 406.262px;" id="elylc8uyv0kl" class="animable"></path><path d="M260.26,402.71a12.66,12.66,0,0,0,2.47.7.17.17,0,0,0,.19-.09.2.2,0,0,0,0-.23c-.28-.32-2.77-3.15-3.87-3a.57.57,0,0,0-.46.36,1.13,1.13,0,0,0,0,1A3.2,3.2,0,0,0,260.26,402.71Zm2,.2c-1.77-.4-3-1-3.3-1.6a.74.74,0,0,1,0-.68.23.23,0,0,1,.18-.15C259.68,400.4,261.18,401.78,262.22,402.91Z" style="fill: #bb86fc; transform-origin: 260.715px 401.749px;" id="elk8ubhqg1j" class="animable"></path><path d="M262.69,403.4a.17.17,0,0,0,.12,0,.2.2,0,0,0,.13-.14c0-.09.53-2.25-.15-3.46a1.49,1.49,0,0,0-.88-.72c-.51-.15-.74.05-.84.24-.44.82.78,3.23,1.55,4Zm-.76-3.89a1.1,1.1,0,0,1,.54.49,4.94,4.94,0,0,1,.17,2.8c-.73-1-1.51-2.78-1.24-3.27,0,0,.09-.16.42-.06Z" style="fill: #bb86fc; transform-origin: 262.057px 401.219px;" id="elxz3q5jv6u2a" class="animable"></path><path d="M89.47,164.52l-.9-.36c1-2.49,2.11-5,3.29-7.35l.88.43C91.57,159.61,90.47,162.06,89.47,164.52Z" style="fill: #bb86fc; transform-origin: 90.655px 160.665px;" id="elx5bhtq7loeh" class="animable"></path><path d="M82.8,189.18l-1-.14A107.77,107.77,0,0,1,86.41,170l.93.32A106.63,106.63,0,0,0,82.8,189.18Z" style="fill: #bb86fc; transform-origin: 84.57px 179.59px;" id="elrncsb9jbzu" class="animable"></path><g id="elwpa580009tp"><circle cx="189.55" cy="204.84" r="105.44" style="fill: #bb86fc; transform-origin: 189.55px 204.84px; transform: rotate(-45deg);" class="animable" id="elswvit1tjfz"></circle></g><g id="eliyrfy2n52e"><circle cx="195.48" cy="182.76" r="79.21" style="fill: rgb(255, 255, 255); opacity: 0.07; transform-origin: 195.48px 182.76px;" class="animable" id="el9jk9mqjah0t"></circle></g><path d="M237.61,143.74c-.15,3-3.79,5.33-8.13,5.11s-7.73-2.86-7.57-5.91,3.79-5.32,8.13-5.1S237.77,140.7,237.61,143.74Z" style="fill: rgb(38, 50, 56); transform-origin: 229.76px 143.345px;" id="elqtnmhnj1lja" class="animable"></path><path d="M156.57,148.74c.58,3-2.41,6.08-6.67,6.91s-8.19-.92-8.77-3.91,2.4-6.09,6.67-6.92S156,145.75,156.57,148.74Z" style="fill: rgb(38, 50, 56); transform-origin: 148.85px 150.235px;" id="elu7g5jhbm0om" class="animable"></path><ellipse cx="146.47" cy="166.1" rx="14.79" ry="5.51" style="fill: #bb86fc; transform-origin: 146.47px 166.1px;" id="el0y5r0oe5z848" class="animable"></ellipse><path d="M251,154.73c.45,2.9-5.49,6.24-13.26,7.46s-14.45-.15-14.91-3.05,5.49-6.23,13.26-7.45S250.57,151.83,251,154.73Z" style="fill: #bb86fc; transform-origin: 236.914px 156.939px;" id="elok3k7m7rsq" class="animable"></path><path d="M170.38,172.57c7.62-2.38,27.56-7.64,49.73-5.63a2.46,2.46,0,0,1,2.17,2.79c-2.46,19.32-38.18,23.28-53,6.76A2.42,2.42,0,0,1,170.38,172.57Z" style="fill: rgb(38, 50, 56); transform-origin: 195.487px 176.67px;" id="elr104jqj61tr" class="animable"></path><g id="el120fn8gk3ngl"><path d="M204.55,175.07a17.4,17.4,0,0,0-3.67,1.53,13.81,13.81,0,0,0-9.63-.4c-4.83,1.4-8.58,4.68-9.87,8.21a41.47,41.47,0,0,0,22.9,1.47c5.39-1.2,10.18-3.55,13.47-6.95C216,175.07,210.5,173.34,204.55,175.07Z" style="fill: #bb86fc; opacity: 0.6; transform-origin: 199.565px 180.622px;" class="animable" id="el19ja9ku3plk"></path></g><path d="M306.7,204.22,299.56,200,299,210.71s6.8,2.16,8.19-1.47Z" style="fill: rgb(255, 139, 123); transform-origin: 303.095px 205.674px;" id="ely8uzcj7fqk" class="animable"></path><polygon points="290.97 200.79 290.96 209.58 298.99 210.71 299.56 200.04 290.97 200.79" style="fill: rgb(255, 139, 123); transform-origin: 295.26px 205.375px;" id="elwu9i66vwydh" class="animable"></polygon></g><defs>     <filter id="active" height="200%">         <feMorphology in="SourceAlpha" result="DILATED" operator="dilate" radius="2"></feMorphology>                <feFlood flood-color="#32DFEC" flood-opacity="1" result="PINK"></feFlood>        <feComposite in="PINK" in2="DILATED" operator="in" result="OUTLINE"></feComposite>        <feMerge>            <feMergeNode in="OUTLINE"></feMergeNode>            <feMergeNode in="SourceGraphic"></feMergeNode>        </feMerge>    </filter>    <filter id="hover" height="200%">        <feMorphology in="SourceAlpha" result="DILATED" operator="dilate" radius="2"></feMorphology>                <feFlood flood-color="#ff0000" flood-opacity="0.5" result="PINK"></feFlood>        <feComposite in="PINK" in2="DILATED" operator="in" result="OUTLINE"></feComposite>        <feMerge>            <feMergeNode in="OUTLINE"></feMergeNode>            <feMergeNode in="SourceGraphic"></feMergeNode>        </feMerge>            <feColorMatrix type="matrix" values="0   0   0   0   0                0   1   0   0   0                0   0   0   0   0                0   0   0   1   0 "></feColorMatrix>    </filter></defs>
              </svg>
          </div>



        <div class="col-lg-6 col-md-12 mb-4 mb-md-0 form-comtainer">
          <div class="form-style-6">
             <div class="form-header">
                <h6 class="display">Get in Touch</h6>
              </div>
                <form name="form1" action="https://formcarry.com/s/vjKNw2JNP7" method="POST" accept-charset="UTF-8" >
                  <input id="name" type="text" name="name" placeholder="Your Name" required/>
                  <input id="email" type="email" name="email" placeholder="Email Address" required/>                  
                  <textarea id="textArea" name="message" placeholder="Type your Message" required></textarea>
              
                  <div id="main">
                    <button id="lnch" type="button" value="Send" >Send</button>
                    <div id="lnch_btn"><i class="fas fa-space-shuttle"></i></div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
    </div>


    

    <div class="rounded-social-buttons tag">
    <a class="social-button twitter" href="https://twitter.com/Mih_Nig_Afe" target="_blank">
    <!-- SVG code for twitter icon -->
    <svg class="twitter-icon-footer" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 500 500" enable-background="new 0 0 500 500"
        xml:space="preserve">
        <!-- bird body -->
        <path id="body" fill="#5DA8DC" d="M142.9,364.1c-1.6,1-3,1.7-4,2.3c-3,1.5-7.9,3.8-14.9,6.9c-7,3.1-14.7,5.7-23.1,7.9
                          c-8.4,2.2-15.6,3.8-21.8,4.7c-6.2,0.9-12.2,1.5-18.1,1.8s-11.4,0.3-16.7,0c-5.2-0.3-8.5-0.5-9.6-0.6l-1.8-0.2l-0.4-0.1l-0.4-0.1v0.8
                          h0.2l0.1,0.1l0.1,0.1l0.1,0.1l0.1,0.1l0.1,0.1H33l0.4,0.2l0.4,0.2l0.1,0.1l0.1,0.1l0.1,0.1l0.1,0.1l3.6,2.2c2.4,1.5,4.5,2.6,6.4,3.6
                          s6.6,3.3,14.1,7.1c7.6,3.7,16.6,7.4,27.2,11.1s18.6,6.2,24,7.4c5.4,1.3,12.8,2.6,22.2,3.9s14.9,2.1,16.3,2.2
                          c1.5,0.1,3.3,0.3,5.5,0.4l3.3,0.2v0.2h25.6v-0.2l14-1.3c9.3-0.9,17.6-2.1,25-3.3c7.3-1.3,14.9-3.1,22.8-5.5
                          c7.9-2.4,15.3-4.9,22.4-7.7c7.1-2.8,13.7-5.7,19.7-9c6.1-3.2,11.3-6,15.6-8.5c4.3-2.5,9.1-5.6,14.2-9.3c5.2-3.7,10-7.5,14.6-11.2
                          s7.1-5.9,7.7-6.4c0.6-0.6,4-4,10.2-10.2c6.2-6.3,11.3-11.9,15.4-16.8c4-5,8-10.3,12-15.9c3.9-5.6,8.3-12.5,13-20.6
                          s9.2-17.3,13.5-27.5s8-20.7,11-31.5c3-10.7,5.2-20.4,6.7-28.9s2.4-16.5,3-23.8c0.5-7.3,0.8-13.9,0.9-19.7s2.5-10.8,7.4-14.8
                          s9.9-8.5,15-13.7c5.1-5.1,7.9-8,8.3-8.7c0.5-0.7,0.9-1.1,1.1-1.2c0.3-0.1,2.1-2.3,5.3-6.7c3.3-4.4,5-6.6,5-6.7l0.1-0.1l0.2-0.4
                          l0.2-0.4l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.2-0.1l0.2-0.1V115l-1,0.3l-0.8,0.1
                          l-0.4,0.2l-0.4,0.2l-0.4,0.2l-0.4,0.2l-0.6,0.2l-1.2,0.4l-10.6,3.6c-6.7,2.2-13.7,4.1-21,5.7l-11,2.4h-1.9l0.1-0.1l0.1-0.1l0.1-0.1
                          l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.4-0.2l0.4-0.2l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.4-0.2l0.4-0.2l1.9-1.3
                          c1.3-0.9,2.4-1.8,3.3-2.8s2.8-2.7,5.6-5.1c2.8-2.4,6-6,9.5-10.7s6.5-9.4,8.8-14s3.6-7.2,3.7-7.9c0.1-0.6,0.3-1.2,0.4-1.6l0.2-0.6
                          l0.2-0.4l0.2-0.4l0.2-0.6l0.2-0.6l0.1-0.6l0.1-0.6l-0.4,0.1l-0.4,0.1l-0.1,0.1l-0.1,0.1l-0.1,0.1l-0.1,0.1l-0.4,0.2l-0.4,0.2
                          l-0.4,0.2l-0.4,0.2l-0.1,0.1c-0.1,0.1-0.8,0.4-1.9,1.2c-1.2,0.7-4.7,2.4-10.5,5s-11.6,5-17.5,7.1s-11.4,3.7-16.5,4.9
                          s-8.8,0.5-11.3-1.9c-2.4-2.4-5.2-4.7-8.3-6.9c-3.1-2.2-6.5-4.3-10.4-6.4c-3.8-2.1-7.7-3.9-11.8-5.5c-4-1.6-8.6-2.9-13.5-3.8
                          l-7.4-1.5h-20.5v0.1c0,0.1-1.7,0.4-5.1,0.9s-7.6,1.6-12.6,3.3c-5,1.7-10.4,4.2-16.3,7.4c-5.9,3.3-11.1,7-15.7,11.2
                          s-8.3,8.3-11.2,12.2c-2.9,3.9-5.2,7.4-7,10.5C221.5,163.3,231.3,307,142.9,364.1z" />

        <!-- wing1 -->
        <path id="wing1" fill="#5DA8DC" d="M233.2,181.5c-5-0.5-12.4-1.7-22.2-3.6c-9.8-1.8-16.8-3.3-20.8-4.5s-11.1-3.7-21.2-7.4
                          c-10.1-3.8-19.5-8-28.3-12.8c-8.8-4.7-16.8-9.5-24-14.4s-13.1-9.1-17.5-12.8c-4.5-3.7-7.1-6-7.9-7s-1.5-1.6-1.9-1.8
                          c-0.5-0.2-3.2-2.7-8-7.6s-9.1-9.2-12.6-13.2l-5.3-5.9l-0.1-0.1l-0.1-0.1L63.1,90l-0.2-0.4l-0.1-0.1l-0.1-0.1l-0.1-0.1l-0.1-0.1
                          l-0.1-0.1L62.3,89l-0.1-0.1l0,0.1l-0.1,0.1L62,89.2l0,0.1l-0.1,0.1L61.7,90l-0.2,0.6L57.9,98c-2.2,5-3.9,9.7-5.1,14.1
                          c-1.2,4.5-1.9,8.5-2.4,12c-0.4,3.5-0.5,7.8-0.4,12.8s0.7,10,1.8,15.1c1,5.1,2.6,10.2,4.6,15.2c2.1,5.1,4,9.2,5.8,12.5
                          c1.8,3.2,3.9,6.4,6.3,9.2c2.4,2.9,4.7,5.6,7.1,8.3s4.6,4.7,6.7,6.4c2.2,1.6,3.3,2.5,3.4,2.5l0.1,0.1l0.4,0.2l0.4,0.2l0.1,0.1
                          l0.1,0.1l0.1,0.1L87,207l0.4,0.2l0.4,0.2l0.1,0.1l0.1,0.1l0.1,0.1l0.1,0.1l0.1,0.1l0.1,0.1l0.1,0.1l0.1,0.1H85l-1.8-0.2
                          c-1.2-0.1-4.8-0.9-10.8-2.2s-11.1-2.9-15.1-4.7l-6.1-2.8l-0.4-0.2l-0.4-0.2l-0.4-0.2l-0.4-0.2l-0.4-0.1l-0.4-0.1l0.7,8.3
                          c0.4,5.5,1.4,11,2.8,16.3c1.5,5.4,3.6,11,6.5,16.9s6.2,11.1,9.8,15.5c3.7,4.5,7,8.1,10.1,11c3.1,2.8,6.3,5.4,9.8,7.8
                          c3.4,2.4,8,4.8,13.8,7.3s9.3,3.9,10.6,4.3c1.3,0.4,2.2,0.6,2.8,0.8l0.8,0.2l0.8,0.2l0.8,0.2l0.8,0.2l0.8,0.2l-0.1,0.1l-0.1,0.1
                          l-0.1,0.1l-0.1,0.1l-1,0.2l-1,0.2l-0.8,0.2c-0.5,0.1-1.9,0.4-4.3,0.8s-6.6,0.6-12.8,0.8c-6.2,0.1-10.7,0-13.5-0.4l-4.3-0.6L81,288
                          l-0.6-0.1l0.1,0.4l0.1,0.4l0.2,0.6l0.2,0.6l3.2,7.2c2.2,4.8,4.4,9,6.7,12.7c2.3,3.7,5.1,7.2,8.3,10.7c3.2,3.5,5.8,6.2,7.9,8
                          c2.1,1.9,5.3,4.3,9.9,7.2c4.6,2.9,9.3,5.4,14.1,7.4c4.9,2.1,9.4,3.7,13.5,4.7c4.2,1,7.3,1.6,9.2,1.8c1.9,0.1,4,0.3,6.1,0.4l3.1,0.2
                          c117.9-117.9,82.9-167.7,82.9-167.7l-2.8-0.1C241.6,182.3,238.2,181.9,233.2,181.5z" />
        <!-- wing2 -->
        <path id="wing2" fill="#5DA8DC" d="M233.2,181.5c-5-0.5-12.4-1.7-22.2-3.6c-9.8-1.8-16.8-3.3-20.8-4.5s-11.1-3.7-21.2-7.4
                          c-10.1-3.8-19.5-8-28.3-12.8c-8.8-4.7-16.8-9.5-24-14.4s-13.1-9.1-17.5-12.8c-4.5-3.7-7.1-6-7.9-7s-1.5-1.6-1.9-1.8
                          c-0.5-0.2-3.2-2.7-8-7.6s-9.1-9.2-12.6-13.2l-5.3-5.9l-0.1-0.1l-0.1-0.1L63.1,90l-0.2-0.4l-0.1-0.1l-0.1-0.1l-0.1-0.1l-0.1-0.1
                          l-0.1-0.1L62.3,89l-0.1-0.1l0,0.1l-0.1,0.1L62,89.2l0,0.1l-0.1,0.1L61.7,90l-0.2,0.6L57.9,98c-2.2,5-3.9,9.7-5.1,14.1
                          c-1.2,4.5-1.9,8.5-2.4,12c-0.4,3.5-0.5,7.8-0.4,12.8s0.7,10,1.8,15.1c1,5.1,2.6,10.2,4.6,15.2c2.1,5.1,4,9.2,5.8,12.5
                          c1.8,3.2,3.9,6.4,6.3,9.2c2.4,2.9,4.7,5.6,7.1,8.3s4.6,4.7,6.7,6.4c2.2,1.6,3.3,2.5,3.4,2.5l0.1,0.1l0.4,0.2l0.4,0.2l0.1,0.1
                          l0.1,0.1l0.1,0.1L87,207l0.4,0.2l0.4,0.2l0.1,0.1l0.1,0.1l0.1,0.1l0.1,0.1l0.1,0.1l0.1,0.1l0.1,0.1l0.1,0.1H85l-1.8-0.2
                          c-1.2-0.1-4.8-0.9-10.8-2.2s-11.1-2.9-15.1-4.7l-6.1-2.8l-0.4-0.2l-0.4-0.2l-0.4-0.2l-0.4-0.2l-0.4-0.1l-0.4-0.1l0.7,8.3
                          c0.4,5.5,1.4,11,2.8,16.3c1.5,5.4,3.6,11,6.5,16.9s6.2,11.1,9.8,15.5c3.7,4.5,7,8.1,10.1,11c3.1,2.8,6.3,5.4,9.8,7.8
                          c3.4,2.4,8,4.8,13.8,7.3s9.3,3.9,10.6,4.3c1.3,0.4,2.2,0.6,2.8,0.8l0.8,0.2l0.8,0.2l0.8,0.2l0.8,0.2l0.8,0.2l-0.1,0.1l-0.1,0.1
                          l-0.1,0.1l-0.1,0.1l-1,0.2l-1,0.2l-0.8,0.2c-0.5,0.1-1.9,0.4-4.3,0.8s-6.6,0.6-12.8,0.8c-6.2,0.1-10.7,0-13.5-0.4l-4.3-0.6L81,288
                          l-0.6-0.1l0.1,0.4l0.1,0.4l0.2,0.6l0.2,0.6l3.2,7.2c2.2,4.8,4.4,9,6.7,12.7c2.3,3.7,5.1,7.2,8.3,10.7c3.2,3.5,5.8,6.2,7.9,8
                          c2.1,1.9,5.3,4.3,9.9,7.2c4.6,2.9,9.3,5.4,14.1,7.4c4.9,2.1,9.4,3.7,13.5,4.7c4.2,1,7.3,1.6,9.2,1.8c1.9,0.1,4,0.3,6.1,0.4l3.1,0.2
                          c117.9-117.9,82.9-167.7,82.9-167.7l-2.8-0.1C241.6,182.3,238.2,181.9,233.2,181.5z" />
    </svg>
    </a>

    <a class="social-button linkedin" href="" target="_blank" >
    <!-- svg code for linkedin icon -->
      <svg class="linkedin-icon-footer" xmlns="http://www.w3.org/2000/svg" width="35" viewBox="0 0 24 24" fill="#0e76a8"
        stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather feather-linkedin">
        <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
        <rect x="2" y="9" width="4" height="12"></rect>
        <circle cx="4" cy="4" r="2"></circle>
      </svg>
    </a>



    <a class="social-button instagram" href="https://www.instagram.com/mih_nig_afe/" target="_blank" >
    <!-- svg code for instagram icon -->
      <svg class="instagram-icon-footer" x="0px" y="0px" viewBox="0 0 202.5 202.5"
        style="enable-background:new 0 0 202.5 202.5;">
        <circle id="littleCircle" class="st0" cx="101" cy="101.5" r="18.9" />
        <circle id="shutter" class="st0" cx="101" cy="101.5" r="8" />
        <circle id="lens" class="st0" cx="125.5" cy="78.6" r="2.9" />
        <path id="camera" class="st0" d="M79,60.5h44c10.5,0,19,8.5,19,19v44c0,10.5-8.5,19-19,19H79c-10.5,0-19-8.5-19-19v-44                                                                            C60,69,68.5,60.5,79,60.5z" />
      </svg>
    </a>

    <a class="social-button github" href="https://github.com/Mih-Nig-Afe" target="_blank">
    <!-- SVG code for Github icon -->
    <svg class="github-icon-footer" width="45px" height="45px" viewBox="0 0 300 300">
        <!-- body -->
        <path id="body" d="M112.5,234.4v43.1c24.5,7.5,50.5,7.5,75,0V225c-0.4-11.1-4.9-21.6-12.7-29.6c30-3.4,59.5-23.5,59.5-64.1
                                    c0.9-13.9-3.3-27.7-11.8-38.7c4.1-11.6,3.7-24.4-1.3-35.7c0,0-11.2-3.7-37.5,13.8c-22.1-6.1-45.3-6.1-67.4,0
                                    C90.6,53.3,78.9,56.8,78.9,56.8c-5,11.3-5.4,24.1-1.3,35.7c-8.5,11.1-12.7,24.8-11.9,38.7c0.2,33.8,26.4,61.8,60.2,64.1
                                    c-5,4.7-8.1,11.1-8.6,18" />
        <!-- arm                             -->
        <path id="arm" d="M125.2,213.4c-13.9,7.8-31.5,2.8-39.3-11.2c-4.3-7.7-12-12.8-20.7-13.8c-11.2,0-4.6,6.4,0,9.4
                                    c6,5.1,10.8,11.5,13.8,18.8c2.9,7.4,7.7,24.1,41.5,17.8" />
    </svg>
    </a>


    
    </div>
    </div>
</footer>
`);

//"Scroll to top" button
let upArrow = $(`
  <button id="btnScrollToTop" onclick="scrollToTop()"><i class="fas fa-2x fa-angle-up"></i></button>
  <link rel="stylesheet" type="text/css" href="./css/style.css" />
  })
`);

$(document).ready(function () {
  // updating the color of the swiper bullets (initial update of color)
  updateColorOfSwiperBullets(localStorage.getItem("lightMode"));

  //function for the "Scroll To Top" button to detect the footer
  $(window).scroll(function () {
    //The button will be hidden until we scroll more than the window's height
    if ($(window).scrollTop() < $(window).height()) {
      $("#btnScrollToTop").css("visibility", "hidden");
    } else {
      $("#btnScrollToTop").css("visibility", "visible");
      //The button will change it's color when it hits the footer
      if (
        $(window).scrollTop() + $(window).height() >
        $(document).height() - 838
      ) {
        // 838 should be changed if footer's height is changed so that the button changes it's color exactly when it hits the footer (preferably 14 less than the computer height of the footer)
        $("#btnScrollToTop").css("background-color", "#6a00bb");
      } else {
        $("#btnScrollToTop").css("background-color", "#6a00bb");
      }
    }
  });
});

//function to scroll to top
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: "smooth",
  });
};

// Window Loads
$(function () {
  let bodyElement = $(`body`);
  bodyElement.prepend(header);
  bodyElement.append(footer);
  bodyElement.append(upArrow);
  $("#btnScrollToTop").css("visibility", "hidden");

  //toggler hamburger functions
  const menuBtn = document.querySelector(".navbar-toggler");
  let menuOpen = false;
  menuBtn.addEventListener("click", () => {
    if (!menuOpen) {
      menuBtn.classList.add("open");
      menuOpen = true;
    } else {
      menuBtn.classList.remove("open");
      menuOpen = false;
    }
  });
});

// function for toggling hamburger is-active class

$(function () {
  $("#js-hamburger").on("click", function () {
    $(this).toggleClass("is-active");
  });
});

// Navbar current page highlight

let loader = document.querySelector(".loader-container");

window.addEventListener("load", vanish);

function vanish() {
  loader.classList.add("disappear");
}
$(function () {
  $("a.nav-link").each(function () {
    if ($(this).prop("href") == window.location.href) {
      $(this).addClass("current-link");
    }
  });
});

//function to remove underline on hover

$(document).ready(function () {
  $("a.nav-link").hover(
    function () {
      $(this).removeClass("current-link");
    },
    function () {
      if ($(this).prop("href") == window.location.href) {
        $(this).addClass("current-link");
      }
    }
  );
});

//consistent light mode for page change
if (localStorage.getItem("lightMode") == "light") {
  var app = document.getElementsByTagName("HTML")[0];
  app.setAttribute("light-mode", "light");

  //to add dark theme to nav bar after its been loaded
  window.addEventListener("load", function () {
    var nav = document.getElementById("navbar");
    nav.classList.remove("dark-theme");
    document.getElementById("dark_toggler").checked = false;
  });

  var sc = document.getElementsByClassName("socialicon");
  for (var i = 0; i < sc.length; i++) {
    sc[i].classList.remove("dsc");
  }
} else {
  localStorage.setItem("lightMode", "dark");
}

function toggle_light_mode() {
  console.log(localStorage.getItem("lightMode"));
  var app = document.getElementsByTagName("HTML")[0];
  var nav = document.getElementById("navbar");
  if (localStorage.lightMode == "dark") {
    localStorage.lightMode = "light";
    app.setAttribute("light-mode", "light");
    nav.classList.remove("dark-theme");
    var sc = document.getElementsByClassName("socialicon");
    for (var i = 0; i < sc.length; i++) {
      sc[i].classList.remove("dsc");
    }
  } else {
    nav.classList.add("dark-theme");
    localStorage.lightMode = "dark";
    app.setAttribute("light-mode", "dark");
    var sc = document.getElementsByClassName("socialicon");
    for (var i = 0; i < sc.length; i++) {
      sc[i].classList.add("dsc");
    }
  }

  // updating the swiper bullets
  updateColorOfSwiperBullets(localStorage.getItem("lightMode"));
}

// function to update swiper bullets
function updateColorOfSwiperBullets(lightMode) {
  document.querySelectorAll(".swiper-pagination-bullet").forEach((bullet) => {
    if (lightMode == "light") {
      bullet.style.backgroundColor = "blue";
    } else {
      bullet.style.backgroundColor = "white";
    }
  });
}

window.addEventListener("storage", function () {
  if (localStorage.lightMode == "dark") {
    app.setAttribute("light-mode", "dark");
  } else {
    app.setAttribute("light-mode", "light");
  }
});

// Function to remove scroll bar during preload
$(window).on("load", function () {
  setTimeout(function () {
    $(".no-scroll-preload").css("overflow", "visible");
  }, 1000);
  $(".loader-container").fadeOut(2500);
});

//send button animation


$(function submitAnimation() {
  const name = document.querySelector("#name")
  const emailAdress = document.querySelector("#email")
  const text = document.querySelector("#textArea")
  const emailPattern = /[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?/;

  $("#lnch").on("click", function () {

    // Check if the name field is empty or contains a number
    if (name.value == "" || (/\d/.test(name.value))) {
      swal("Error !","Please enter a valid name !","error");
      return;
    }
    // Check if the email field is empty or email is not valid ex: test@@email.com
    else if (emailAdress.value == "" || !(emailPattern.test(emailAdress.value))) {
      swal("Error !","Please enter a valid email !","error");
      return;
    }
    // Check if the message field is empty
    else if (text.value == "") {
      swal("Error !","Please enter a valid message !","error");
      return;
    }
    else {

      setTimeout(function () {
        $("#lnch").addClass("launching").text("Sending");
        $("#lnch_btn").addClass("launching");
      }, 0);
      setTimeout(function () {
        $("#lnch").addClass("launched").text("SENT");
        $("#lnch_btn").addClass("launched");
      }, 1500);
      // Wait for 2.2 seconds so that the send button animation can be fully played before submitting the form
      setTimeout(() => {
        document.querySelector('form').submit();
      }, 2200);
    }
  });
});
